.class Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1$1;
.super Ljava/lang/Object;
.source "LiveChannelMobileActivity.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic this$2:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;


# direct methods
.method constructor <init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1$1;->this$2:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1$1;->this$2:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;

    iget-object v0, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;->this$1:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;

    iget-object v0, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    .line 2
    iget-boolean v1, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->isChannelMarkedAsDown:Z

    if-eqz v1, :cond_0

    .line 3
    invoke-direct {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->markChannelAsUp()V

    .line 4
    iget-object v1, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->content_url:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->access$1100(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;Ljava/lang/String;)V

    .line 5
    const-string v1, "LiveChannelMobileActivity"

    const-string v2, "Channel recovered, reconnecting..."

    invoke-static {v1, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    :cond_0
    return-void
.end method
