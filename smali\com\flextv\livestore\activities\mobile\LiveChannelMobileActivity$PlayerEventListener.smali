.class Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;
.super Ljava/lang/Object;
.source "LiveChannelMobileActivity.java"

# interfaces
.implements Lcom/google/android/exoplayer2/Player$Listener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "PlayerEventListener"
.end annotation


# instance fields
.field public final synthetic this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;


# direct methods
.method private constructor <init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$1;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;-><init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    return-void
.end method


# virtual methods
.method public final synthetic onAudioAttributesChanged(Lcom/google/android/exoplayer2/audio/AudioAttributes;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onAudioAttributesChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/audio/AudioAttributes;)V

    return-void
.end method

.method public final synthetic onAudioSessionIdChanged(I)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onAudioSessionIdChanged(Lcom/google/android/exoplayer2/Player$Listener;I)V

    return-void
.end method

.method public final synthetic onAvailableCommandsChanged(Lcom/google/android/exoplayer2/Player$Commands;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onAvailableCommandsChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/Player$Commands;)V

    return-void
.end method

.method public final synthetic onCues(Lcom/google/android/exoplayer2/text/CueGroup;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onCues(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/text/CueGroup;)V

    return-void
.end method

.method public final synthetic onCues(Ljava/util/List;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onCues(Lcom/google/android/exoplayer2/Player$Listener;Ljava/util/List;)V

    return-void
.end method

.method public final synthetic onDeviceInfoChanged(Lcom/google/android/exoplayer2/DeviceInfo;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onDeviceInfoChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/DeviceInfo;)V

    return-void
.end method

.method public final synthetic onDeviceVolumeChanged(IZ)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onDeviceVolumeChanged(Lcom/google/android/exoplayer2/Player$Listener;IZ)V

    return-void
.end method

.method public final synthetic onEvents(Lcom/google/android/exoplayer2/Player;Lcom/google/android/exoplayer2/Player$Events;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onEvents(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/Player;Lcom/google/android/exoplayer2/Player$Events;)V

    return-void
.end method

.method public final synthetic onIsLoadingChanged(Z)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onIsLoadingChanged(Lcom/google/android/exoplayer2/Player$Listener;Z)V

    return-void
.end method

.method public final synthetic onIsPlayingChanged(Z)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onIsPlayingChanged(Lcom/google/android/exoplayer2/Player$Listener;Z)V

    return-void
.end method

.method public final synthetic onLoadingChanged(Z)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onLoadingChanged(Lcom/google/android/exoplayer2/Player$Listener;Z)V

    return-void
.end method

.method public final synthetic onMaxSeekToPreviousPositionChanged(J)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onMaxSeekToPreviousPositionChanged(Lcom/google/android/exoplayer2/Player$Listener;J)V

    return-void
.end method

.method public final synthetic onMediaItemTransition(Lcom/google/android/exoplayer2/MediaItem;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onMediaItemTransition(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/MediaItem;I)V

    return-void
.end method

.method public final synthetic onMediaMetadataChanged(Lcom/google/android/exoplayer2/MediaMetadata;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onMediaMetadataChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/MediaMetadata;)V

    return-void
.end method

.method public final synthetic onMetadata(Lcom/google/android/exoplayer2/metadata/Metadata;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onMetadata(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/metadata/Metadata;)V

    return-void
.end method

.method public final synthetic onPlayWhenReadyChanged(ZI)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPlayWhenReadyChanged(Lcom/google/android/exoplayer2/Player$Listener;ZI)V

    return-void
.end method

.method public final synthetic onPlaybackParametersChanged(Lcom/google/android/exoplayer2/PlaybackParameters;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPlaybackParametersChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/PlaybackParameters;)V

    return-void
.end method

.method public onPlaybackStateChanged(I)V
    .locals 1

    const/4 v0, 0x4

    if-ne p1, v0, :cond_0

    .line 1
    iget-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    invoke-static {p1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->access$1000(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    .line 2
    iget-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iget-object v0, p1, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->content_url:Ljava/lang/String;

    invoke-static {p1, v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->access$1100(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const/4 v0, 0x3

    if-ne p1, v0, :cond_1

    .line 3
    iget-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    const/4 v0, 0x0

    iput v0, p1, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->error_count:I

    :cond_1
    :goto_0
    return-void
.end method

.method public final synthetic onPlaybackSuppressionReasonChanged(I)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPlaybackSuppressionReasonChanged(Lcom/google/android/exoplayer2/Player$Listener;I)V

    return-void
.end method

.method public onPlayerError(Lcom/google/android/exoplayer2/PlaybackException;)V
    .locals 6

    .line 1
    iget v0, p1, Lcom/google/android/exoplayer2/PlaybackException;->errorCode:I

    const/16 v1, 0x3ea

    if-ne v0, v1, :cond_0

    .line 2
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    invoke-direct {p0, v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->handleSourceErrorWithDelay(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    goto :goto_0

    .line 3
    :cond_0
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iget v1, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->error_count:I

    const/4 v2, 0x5

    if-le v1, v2, :cond_1

    .line 4
    invoke-static {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->access$1000(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    .line 5
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    invoke-direct {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->markChannelAsDown()V

    goto :goto_0

    :cond_1
    add-int/lit8 v1, v1, 0x1

    .line 6
    iput v1, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->error_count:I

    .line 7
    mul-int/lit16 v2, v1, 0x3e8

    const/16 v3, 0x1388

    invoke-static {v2, v3}, Ljava/lang/Math;->min(II)I

    move-result v2

    .line 8
    invoke-static {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->access$1000(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    .line 9
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    int-to-long v2, v2

    invoke-direct {p0, v0, v2, v3}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->retryPlaybackWithDelay(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;J)V

    :goto_0
    return-void
.end method

.method private handleSourceErrorWithDelay(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V
    .locals 4

    .line 1
    invoke-direct {p0, p1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->isNetworkAvailable(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2
    const-wide/16 v0, 0x1388

    invoke-direct {p0, p1, v0, v1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->retryPlaybackWithDelay(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;J)V

    return-void

    .line 3
    :cond_0
    invoke-static {p1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->access$1000(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    .line 4
    const-wide/16 v0, 0x7d0

    invoke-direct {p0, p1, v0, v1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;->retryPlaybackWithDelay(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;J)V

    return-void
.end method

.method private retryPlaybackWithDelay(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;J)V
    .locals 2

    .line 1
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    .line 2
    new-instance v1, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener$1;

    invoke-direct {v1, p0, p1}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener$1;-><init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$PlayerEventListener;Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V

    invoke-virtual {v0, v1, p2, p3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method private isNetworkAvailable(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)Z
    .locals 2

    .line 1
    const-string v0, "connectivity"

    invoke-virtual {p1, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/net/ConnectivityManager;

    .line 2
    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroid/net/ConnectivityManager;->getActiveNetworkInfo()Landroid/net/NetworkInfo;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroid/net/NetworkInfo;->isConnected()Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final synthetic onPlayerErrorChanged(Lcom/google/android/exoplayer2/PlaybackException;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPlayerErrorChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/PlaybackException;)V

    return-void
.end method

.method public final synthetic onPlayerStateChanged(ZI)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPlayerStateChanged(Lcom/google/android/exoplayer2/Player$Listener;ZI)V

    return-void
.end method

.method public final synthetic onPlaylistMetadataChanged(Lcom/google/android/exoplayer2/MediaMetadata;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPlaylistMetadataChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/MediaMetadata;)V

    return-void
.end method

.method public final synthetic onPositionDiscontinuity(I)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPositionDiscontinuity(Lcom/google/android/exoplayer2/Player$Listener;I)V

    return-void
.end method

.method public final synthetic onPositionDiscontinuity(Lcom/google/android/exoplayer2/Player$PositionInfo;Lcom/google/android/exoplayer2/Player$PositionInfo;I)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onPositionDiscontinuity(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/Player$PositionInfo;Lcom/google/android/exoplayer2/Player$PositionInfo;I)V

    return-void
.end method

.method public final synthetic onRenderedFirstFrame()V
    .locals 0

    invoke-static {p0}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onRenderedFirstFrame(Lcom/google/android/exoplayer2/Player$Listener;)V

    return-void
.end method

.method public final synthetic onRepeatModeChanged(I)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onRepeatModeChanged(Lcom/google/android/exoplayer2/Player$Listener;I)V

    return-void
.end method

.method public final synthetic onSeekBackIncrementChanged(J)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onSeekBackIncrementChanged(Lcom/google/android/exoplayer2/Player$Listener;J)V

    return-void
.end method

.method public final synthetic onSeekForwardIncrementChanged(J)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onSeekForwardIncrementChanged(Lcom/google/android/exoplayer2/Player$Listener;J)V

    return-void
.end method

.method public final synthetic onSeekProcessed()V
    .locals 0

    invoke-static {p0}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onSeekProcessed(Lcom/google/android/exoplayer2/Player$Listener;)V

    return-void
.end method

.method public final synthetic onShuffleModeEnabledChanged(Z)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onShuffleModeEnabledChanged(Lcom/google/android/exoplayer2/Player$Listener;Z)V

    return-void
.end method

.method public final synthetic onSkipSilenceEnabledChanged(Z)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onSkipSilenceEnabledChanged(Lcom/google/android/exoplayer2/Player$Listener;Z)V

    return-void
.end method

.method public final synthetic onSurfaceSizeChanged(II)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onSurfaceSizeChanged(Lcom/google/android/exoplayer2/Player$Listener;II)V

    return-void
.end method

.method public final synthetic onTimelineChanged(Lcom/google/android/exoplayer2/Timeline;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onTimelineChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/Timeline;I)V

    return-void
.end method

.method public final synthetic onTrackSelectionParametersChanged(Lcom/google/android/exoplayer2/trackselection/TrackSelectionParameters;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onTrackSelectionParametersChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/trackselection/TrackSelectionParameters;)V

    return-void
.end method

.method public final synthetic onTracksChanged(Lcom/google/android/exoplayer2/Tracks;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onTracksChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/Tracks;)V

    return-void
.end method

.method public final synthetic onVideoSizeChanged(Lcom/google/android/exoplayer2/video/VideoSize;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onVideoSizeChanged(Lcom/google/android/exoplayer2/Player$Listener;Lcom/google/android/exoplayer2/video/VideoSize;)V

    return-void
.end method

.method public final synthetic onVolumeChanged(F)V
    .locals 0

    invoke-static {p0, p1}, Lcom/google/android/exoplayer2/Player$Listener$-CC;->$default$onVolumeChanged(Lcom/google/android/exoplayer2/Player$Listener;F)V

    return-void
.end method
