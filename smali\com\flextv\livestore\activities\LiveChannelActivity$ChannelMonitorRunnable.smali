.class Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable;
.super Ljava/lang/Object;
.source "LiveChannelActivity.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/flextv/livestore/activities/LiveChannelActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "ChannelMonitorRunnable"
.end annotation


# instance fields
.field final synthetic this$0:Lcom/flextv/livestore/activities/LiveChannelActivity;


# direct methods
.method constructor <init>(Lcom/flextv/livestore/activities/LiveChannelActivity;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable;->this$0:Lcom/flextv/livestore/activities/LiveChannelActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable;->this$0:Lcom/flextv/livestore/activities/LiveChannelActivity;

    iget-boolean v0, v0, Lcom/flextv/livestore/activities/LiveChannelActivity;->isChannelMarkedAsDown:Z

    if-nez v0, :cond_0

    return-void

    .line 2
    :cond_0
    iget-object v0, p0, Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable;->this$0:Lcom/flextv/livestore/activities/LiveChannelActivity;

    iget-object v0, v0, Lcom/flextv/livestore/activities/LiveChannelActivity;->content_url:Ljava/lang/String;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1

    .line 3
    new-instance v1, Ljava/lang/Thread;

    new-instance v2, Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable$1;

    invoke-direct {v2, p0, v0}, Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable$1;-><init>(Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable;Ljava/lang/String;)V

    invoke-direct {v1, v2}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 4
    invoke-virtual {v1}, Ljava/lang/Thread;->start()V

    .line 5
    :cond_1
    iget-object v0, p0, Lcom/flextv/livestore/activities/LiveChannelActivity$ChannelMonitorRunnable;->this$0:Lcom/flextv/livestore/activities/LiveChannelActivity;

    iget-object v1, v0, Lcom/flextv/livestore/activities/LiveChannelActivity;->channelMonitorHandler:Landroid/os/Handler;

    if-eqz v1, :cond_2

    .line 6
    const-wide/16 v2, 0x7530

    invoke-virtual {v1, p0, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_2
    return-void
.end method
