.class Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;
.super Ljava/lang/Object;
.source "LiveChannelMobileActivity.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "HeartbeatRunnable"
.end annotation


# instance fields
.field final synthetic this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;


# direct methods
.method constructor <init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iget-object v0, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->exoPlayer:Lcom/google/android/exoplayer2/ExoPlayer;

    if-eqz v0, :cond_3

    .line 2
    invoke-interface {v0}, Lcom/google/android/exoplayer2/Player;->getPlaybackState()I

    move-result v1

    .line 3
    invoke-interface {v0}, Lcom/google/android/exoplayer2/Player;->isPlaying()Z

    move-result v0

    .line 4
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    .line 5
    iget-object v4, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iget-wide v4, v4, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->lastSuccessfulConnection:J

    sub-long v4, v2, v4

    .line 6
    const-wide/32 v6, 0x1d4c0

    cmp-long v6, v4, v6

    if-lez v6, :cond_0

    const/4 v4, 0x2

    if-ne v1, v4, :cond_0

    if-nez v0, :cond_0

    .line 7
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iget-boolean v0, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->isChannelMarkedAsDown:Z

    if-nez v0, :cond_0

    .line 8
    const-string v0, "LiveChannelMobileActivity"

    const-string v1, "Stream appears to be stuck, marking as down"

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 9
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    invoke-direct {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->markChannelAsDown()V

    goto :goto_0

    :cond_0
    const/4 v4, 0x3

    if-ne v1, v4, :cond_1

    if-eqz v0, :cond_1

    .line 10
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iput-wide v2, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->lastSuccessfulConnection:J

    .line 11
    iget-boolean v1, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->isChannelMarkedAsDown:Z

    if-eqz v1, :cond_1

    .line 12
    invoke-direct {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->markChannelAsUp()V

    .line 13
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$HeartbeatRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    iget-object v1, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->heartbeatHandler:Landroid/os/Handler;

    if-eqz v1, :cond_2

    .line 14
    const-wide/16 v2, 0x2710

    invoke-virtual {v1, p0, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    goto :goto_1

    .line 15
    :cond_2
    invoke-direct {v0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;->startHeartbeat()V

    :cond_3
    :goto_1
    return-void
.end method
