.class Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;
.super Ljava/lang/Object;
.source "LiveChannelMobileActivity.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;

.field final synthetic val$url:Ljava/lang/String;


# direct methods
.method constructor <init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;->this$1:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;

    iput-object p2, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;->val$url:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 6

    .line 1
    :try_start_0
    new-instance v0, Ljava/net/URL;

    iget-object v1, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;->val$url:Ljava/lang/String;

    invoke-direct {v0, v1}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    .line 2
    invoke-virtual {v0}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object v0

    check-cast v0, Ljava/net/HttpURLConnection;

    .line 3
    const-string v1, "HEAD"

    invoke-virtual {v0, v1}, Ljava/net/HttpURLConnection;->setRequestMethod(Ljava/lang/String;)V

    .line 4
    const/16 v1, 0x1388

    invoke-virtual {v0, v1}, Ljava/net/URLConnection;->setConnectTimeout(I)V

    .line 5
    invoke-virtual {v0, v1}, Ljava/net/URLConnection;->setReadTimeout(I)V

    .line 6
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/net/HttpURLConnection;->setInstanceFollowRedirects(Z)V

    .line 7
    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->getResponseCode()I

    move-result v1

    .line 8
    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->disconnect()V

    .line 9
    const/16 v0, 0xc8

    if-eq v1, v0, :cond_0

    const/16 v0, 0x12e

    if-eq v1, v0, :cond_0

    const/16 v0, 0x12f

    if-eq v1, v0, :cond_0

    const/16 v0, 0x130

    if-eq v1, v0, :cond_0

    const/16 v0, 0x131

    if-eq v1, v0, :cond_0

    const/16 v0, 0x133

    if-eq v1, v0, :cond_0

    const/16 v0, 0x134

    if-ne v1, v0, :cond_1

    .line 10
    :cond_0
    iget-object v0, p0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;->this$1:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;

    iget-object v0, v0, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable;->this$0:Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity;

    new-instance v1, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1$1;

    invoke-direct {v1, p0}, Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1$1;-><init>(Lcom/flextv/livestore/activities/mobile/LiveChannelMobileActivity$ChannelMonitorRunnable$1;)V

    invoke-virtual {v0, v1}, Landroid/app/Activity;->runOnUiThread(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 11
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :cond_1
    :goto_0
    return-void
.end method
